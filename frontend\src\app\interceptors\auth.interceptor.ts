import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, switchMap, filter, take } from 'rxjs/operators';
import { KeycloakAuthService } from '../services/keycloak-auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {

    private isRefreshing = false;
    private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);

    constructor(private authService: KeycloakAuthService) {}

    intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        // Add auth header if user is authenticated
        const authReq = this.addAuthHeader(req);

        return next.handle(authReq).pipe(
            catchError((error: HttpErrorResponse) => {
                // Handle 401 errors (token expired)
                if (error.status === 401 && !this.isAuthUrl(req.url)) {
                    return this.handle401Error(authReq, next);
                }
                
                return throwError(error);
            })
        );
    }

    private addAuthHeader(req: HttpRequest<any>): HttpRequest<any> {
        const token = this.authService.getAccessToken();
        
        if (token && !this.isAuthUrl(req.url)) {
            return req.clone({
                setHeaders: {
                    Authorization: `Bearer ${token}`
                }
            });
        }
        
        return req;
    }

    private handle401Error(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
        if (!this.isRefreshing) {
            this.isRefreshing = true;
            this.refreshTokenSubject.next(null);

            return this.authService.refreshToken().pipe(
                switchMap(() => {
                    this.isRefreshing = false;
                    this.refreshTokenSubject.next(this.authService.getAccessToken());
                    
                    // Retry the original request with new token
                    return next.handle(this.addAuthHeader(req));
                }),
                catchError((error) => {
                    this.isRefreshing = false;
                    
                    // Refresh failed, logout user
                    this.authService.logout().subscribe();
                    
                    return throwError(error);
                })
            );
        } else {
            // Wait for refresh to complete
            return this.refreshTokenSubject.pipe(
                filter(token => token != null),
                take(1),
                switchMap(() => next.handle(this.addAuthHeader(req)))
            );
        }
    }

    private isAuthUrl(url: string): boolean {
        return url.includes('/auth/login') || 
               url.includes('/auth/refresh') || 
               url.includes('/auth/config');
    }
}
